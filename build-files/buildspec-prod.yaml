version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 794038255033.dkr.ecr.us-east-2.amazonaws.com
      - echo Configuring AWS CLI
      - aws configure set aws_access_key_id ********************
      - aws configure set aws_secret_access_key QU0vP1ZhjhFJSG3mMI7Ep1N66gHsMp3liiq1pbYy
      - aws configure set region us-east-2
      - aws eks update-kubeconfig --region us-east-2 --name coddn
      - docker login --username alexmedina443 --password ************************************
  build:
    commands:
      - cd function
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build --build-arg ENVIRONMENT=prod -t alert-distributor .
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image to ECR...
      - echo Build completed on `date`
      - IMAGE_TAG=$(date +%Y%m%d%H%M%S)
      - echo Pushing the Docker image to ECR with tag $IMAGE_TAG...
      - docker tag alert-distributor:latest 794038255033.dkr.ecr.us-east-2.amazonaws.com/prod/alert-distributor:$IMAGE_TAG
      - docker push 794038255033.dkr.ecr.us-east-2.amazonaws.com/prod/alert-distributor:$IMAGE_TAG
      - kubectl apply -f yaml-files/deployment-prod.yaml
      - kubectl set image deployment/alert-distributor alert-distributor=794038255033.dkr.ecr.us-east-2.amazonaws.com/prod/alert-distributor:$IMAGE_TAG -n prod
artifacts:
  files:
    - '**/*'
  discard-paths: yes
