import common.OpenDroneIDstandards as ODID
import datetime
import json
import os
from bson import ObjectId
from entities.Notification import Notification, LightAlertZone
from dtos.NotificationDto import NotificationDto, NotificationChannels
class AlertDistributerEvent:
    TOPIC_NAME = os.getenv("NOTIFICATION_TOPIC", "NOTIFICATION_DEV_2")
    def __init__(self, dbhand<PERSON>, producer, appSyncHandler):
        self.producer = producer
        self.dbHandler = dbhandler
        self.appSyncHandler = appSyncHandler
        self.tracked_alertzone={}


    async def disribute_event_notification(self, event):
        drone_location = { 'lat': event['INFO']['LAT'], 'lng': event['INFO']['LON'] }
        event['COMPLETE'] = int(event['COMPLETE']) if isinstance(event['COMPLETE'], str) else event['COMPLETE']
        alertZones = self.load_alert_zones(event, drone_location) 

        print("alertZones count: ", len(alertZones))
        for alertZone in alertZones:
            print("notification count for alertzone : ", alertZone['organization'][0]['auth0_id'], len(alertZone['notifications']))

            notification_for_organization = self.prepare_drone_detection_data(event, alertZone)
            if(event['COMPLETE'] == 1):
                await self.appSyncHandler.publish_message(alertZone['organization'][0]['auth0_id'],notification_for_organization)

            if(len(alertZone['notifications']) == 0 and event['COMPLETE'] == 0):
                if(alertZone['isActive'] == True):
                    await self.appSyncHandler.publish_message(alertZone['organization'][0]['auth0_id'],notification_for_organization)
                await self.prepare_and_save_notification_for_org(alertZone, event, alertZone['orgId'])

                users = self.dbHandler.find_users_for_orgs(str(alertZone['organization'][0]['auth0_id']))
                alertZone['users'] = users
                for user in alertZone['users']:
                    notification = self.prepare_notification_for_user(alertZone, event, alertZone['orgId'], user)
                    if(notification.channels.email == True or notification.channels.sms == True):
                        if(alertZone['isActive'] == True):
                            await self.producer.produce_message(self.TOPIC_NAME, json.dumps(notification.to_dict()), notification.inAppTopic)


    def load_alert_zones(self, event, drone_location):
        if(event['COMPLETE'] == 1):
            if event['EVENT_ID'] in self.tracked_alertzone:
                alertZones =  self.tracked_alertzone[event['EVENT_ID']]
                try:
                    self.tracked_alertzone.pop(event['EVENT_ID'])
                except:
                    print("Error in removing the event from tracked alertzone")
            else:
                self.tracked_alertzone = {}
        else:
            alertZones = self.dbHandler.find_alertZones_with_users_to_send(drone_location,event['EVENT_ID'])
            if event['EVENT_ID'] not in self.tracked_alertzone:
                self.tracked_alertzone[event['EVENT_ID']]=alertZones
            else:
                existing_zone_ids = {zone['_id'] for zone in self.tracked_alertzone[event['EVENT_ID']]}
                new_alerts = [zone for zone in alertZones if zone['_id'] not in existing_zone_ids]
                self.tracked_alertzone[event['EVENT_ID']].extend(new_alerts)
        return alertZones

    async def prepare_and_save_notification_for_org(self, alertZone, event, orgId):
        lightAlertZone = LightAlertZone(_id=alertZone['_id'],
                                        name = alertZone['name'])

        notification = Notification(org_id = ObjectId(orgId),
                                    alertZone = lightAlertZone,
                                    event_id = event['EVENT_ID'],
                                    timestamp = datetime.datetime.now(datetime.timezone.utc),
                                    seen = False,
                                    type = "ALERT")
        
        print("saving notification for: ", orgId, "event:", event['EVENT_ID'])
        try:
            self.dbHandler.save_notification(notification.to_entity())
        except Exception as e:
            print(e)
        return notification
        
    def prepare_notification_for_user(self, alertZone, event, orgId, user):
        print("preparing the notification for user: ", user['email'], 
              " EmailAlert: ", user['user_pref'][0]['emailAlert'] if len(user['user_pref']) > 0 else True, 
              " SmsAlert: ", user['user_pref'][0]['smsAlert'] if len(user['user_pref']) > 0 else True)
        
        notificationChannels = NotificationChannels(email = user['user_pref'][0]['emailAlert'] if len(user['user_pref']) > 0 else True,
                                                    sms = user['user_pref'][0]['smsAlert'] if len(user['user_pref']) > 0 else True,
                                                    inapp = True)

        notification = NotificationDto(channels = notificationChannels,
                                       templateId = "drone-alert",
                                       subject = "Drone Alert",
                                       organization = user['org_id'],
                                       inAppTopic = user['sub'],
                                       phoneNumber = user['phone_number'] if 'phone_number' in user else None,
                                       email = user['email'],
                                       data = self.prepare_drone_detection_data(event, alertZone))

        return notification

    def prepare_drone_detection_data(self, event, alertZone):
            try:
                uas_type=ODID.OpenDroneID_basicID_uaType[event['INFO']['INFO']['ODID_basicID_uaType']]
            except:
                uas_type='N/A'
            try:
                location_status=ODID.OpenDroneID_loc_status[event['INFO']['INFO']['ODID_loc_status']]
            except:
                location_status=event['INFO']['INFO']['ODID_loc_status']
            try:
                operator_type=ODID.OpenDroneID_operator_type[event['INFO']['INFO']['ODID_operator_type']]
            except:
                operator_type='N/A'
            try:
                operator_lat=event['INFO']['INFO']['ODID_system_lat']
            except:
                operator_lat='N/A'
            try:
                operator_lon=event['INFO']['INFO']['ODID_system_lon']
            except:
                operator_lon='N/A'
            try:
                drone_heading=event['INFO']['INFO']['ODID_loc_direction']
            except:
                drone_heading='N/A'

            return {
                "alertZoneId": alertZone['_id'],
                "alertZoneName": alertZone['name'],
                "currentDate": datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S"),
                "eventId": event['EVENT_ID'],
                "uasId": event['UAS_ID'],
                "uasType": uas_type,
                "droneLat": event['INFO']['LAT'],
                "droneLng": event['INFO']['LON'],
                "pilotLat": operator_lat,
                "pilotLng": operator_lon,
                "locationStatus": location_status,
                "altitude": event['INFO']['ALTITUDE'],
                "speed": event['INFO']['SPEED'],
                "heading": drone_heading,
                "operatorType":operator_type,
                "complete": event['COMPLETE'],
                "orgName": alertZone['organization'][0]['name'],
                "event": event,
                "envUrl": os.getenv("ENV_URL", "https://dev.aerodefense.tech")
        }
